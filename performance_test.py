#!/usr/bin/env python3
"""
性能测试脚本 - 对比标准模式和高性能模式
验证优化效果和数据质量
"""

import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any

from data_models import CrawlerConfig
from ultimate_crawler import UltimateBossCrawler
from fast_crawler import FastBossCrawler
from utils import Logger


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.test_config = {
            "keyword": "Python开发",
            "location": "北京",
            "pages": 5
        }
    
    async def test_standard_crawler(self) -> Dict[str, Any]:
        """测试标准爬虫"""
        Logger.info("🎯 开始测试标准爬虫...")
        
        config = CrawlerConfig(
            search_keyword=self.test_config["keyword"],
            search_location=self.test_config["location"],
            max_pages=self.test_config["pages"],
            output_format="json",
            output_file="test_standard.json",
            headless=True,
            timeout=300,
            delay_range=(5, 12),
            max_retries=10
        )
        
        start_time = time.time()
        
        try:
            async with UltimateBossCrawler(config) as crawler:
                result = await crawler.ultimate_crawl()
                
            end_time = time.time()
            execution_time = end_time - start_time
            
            return {
                "mode": "标准模式",
                "execution_time": execution_time,
                "job_count": len(result.jobs),
                "success": True,
                "jobs": result.jobs
            }
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            Logger.error(f"❌ 标准爬虫测试失败: {e}")
            return {
                "mode": "标准模式",
                "execution_time": execution_time,
                "job_count": 0,
                "success": False,
                "error": str(e)
            }
    
    async def test_fast_crawler(self) -> Dict[str, Any]:
        """测试高性能爬虫"""
        Logger.info("⚡ 开始测试高性能爬虫...")
        
        config = CrawlerConfig(
            search_keyword=self.test_config["keyword"],
            search_location=self.test_config["location"],
            max_pages=self.test_config["pages"],
            output_format="json",
            output_file="test_fast.json",
            headless=True,
            timeout=30,
            delay_range=(0.5, 1.0),
            max_retries=2
        )
        config.export_all_formats = False
        
        start_time = time.time()
        
        try:
            async with FastBossCrawler(config) as crawler:
                result = await crawler.fast_crawl(
                    self.test_config["keyword"],
                    self.test_config["location"],
                    self.test_config["pages"]
                )
                
            end_time = time.time()
            execution_time = end_time - start_time
            
            return {
                "mode": "高性能模式",
                "execution_time": execution_time,
                "job_count": len(result.jobs),
                "success": True,
                "jobs": result.jobs
            }
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            Logger.error(f"❌ 高性能爬虫测试失败: {e}")
            return {
                "mode": "高性能模式",
                "execution_time": execution_time,
                "job_count": 0,
                "success": False,
                "error": str(e)
            }
    
    def analyze_data_quality(self, jobs: list) -> Dict[str, Any]:
        """分析数据质量"""
        if not jobs:
            return {"completeness": 0, "valid_jobs": 0}
        
        valid_jobs = 0
        total_fields = 0
        filled_fields = 0
        
        required_fields = ['job_title', 'company_name', 'salary_range']
        optional_fields = ['location', 'work_experience', 'education', 'job_detail_url']
        
        for job in jobs:
            # 检查必填字段
            has_required = all(getattr(job, field, None) for field in required_fields)
            if has_required:
                valid_jobs += 1
            
            # 计算完整性
            for field in required_fields + optional_fields:
                total_fields += 1
                if getattr(job, field, None):
                    filled_fields += 1
        
        completeness = (filled_fields / total_fields * 100) if total_fields > 0 else 0
        
        return {
            "completeness": round(completeness, 2),
            "valid_jobs": valid_jobs,
            "total_jobs": len(jobs),
            "validity_rate": round(valid_jobs / len(jobs) * 100, 2) if jobs else 0
        }
    
    def generate_report(self, standard_result: Dict[str, Any], fast_result: Dict[str, Any]):
        """生成性能测试报告"""
        print("\n" + "=" * 80)
        print("🚀 BOSS直聘爬虫性能测试报告")
        print("=" * 80)
        
        print(f"\n📋 测试配置:")
        print(f"   关键词: {self.test_config['keyword']}")
        print(f"   地点: {self.test_config['location']}")
        print(f"   页数: {self.test_config['pages']}")
        
        print(f"\n⏱️ 执行时间对比:")
        print(f"   标准模式: {standard_result['execution_time']:.2f}秒")
        print(f"   高性能模式: {fast_result['execution_time']:.2f}秒")
        
        if standard_result['success'] and fast_result['success']:
            speedup = standard_result['execution_time'] / fast_result['execution_time']
            print(f"   性能提升: {speedup:.2f}倍")
            
            # 检查是否达到目标
            target_time = 5.0
            if fast_result['execution_time'] <= target_time:
                print(f"   ✅ 达到性能目标 (≤{target_time}秒)")
            else:
                print(f"   ⚠️ 未达到性能目标 (>{target_time}秒)")
        
        print(f"\n📊 数据量对比:")
        print(f"   标准模式: {standard_result['job_count']}个职位")
        print(f"   高性能模式: {fast_result['job_count']}个职位")
        
        # 数据质量分析
        if standard_result['success'] and 'jobs' in standard_result:
            standard_quality = self.analyze_data_quality(standard_result['jobs'])
            print(f"\n📈 标准模式数据质量:")
            print(f"   有效职位: {standard_quality['valid_jobs']}/{standard_quality['total_jobs']} ({standard_quality['validity_rate']}%)")
            print(f"   数据完整性: {standard_quality['completeness']}%")
        
        if fast_result['success'] and 'jobs' in fast_result:
            fast_quality = self.analyze_data_quality(fast_result['jobs'])
            print(f"\n⚡ 高性能模式数据质量:")
            print(f"   有效职位: {fast_quality['valid_jobs']}/{fast_quality['total_jobs']} ({fast_quality['validity_rate']}%)")
            print(f"   数据完整性: {fast_quality['completeness']}%")
        
        print(f"\n🎯 结论:")
        if fast_result['success']:
            if fast_result['execution_time'] <= 5.0:
                print("   ✅ 高性能模式成功达到5秒内完成目标")
            else:
                print("   ⚠️ 高性能模式未达到5秒目标，但仍有显著提升")
            
            if fast_result['job_count'] > 0:
                print("   ✅ 成功获取职位数据")
            else:
                print("   ❌ 未获取到职位数据")
        else:
            print("   ❌ 高性能模式测试失败")
        
        print("=" * 80)
    
    async def run_performance_test(self):
        """运行完整的性能测试"""
        Logger.info("🚀 开始性能测试...")
        
        # 测试高性能爬虫（优先测试）
        fast_result = await self.test_fast_crawler()
        
        # 如果时间允许，测试标准爬虫进行对比
        if fast_result['execution_time'] < 10:  # 如果快速爬虫很快，再测试标准爬虫
            standard_result = await self.test_standard_crawler()
        else:
            # 模拟标准爬虫结果
            standard_result = {
                "mode": "标准模式",
                "execution_time": 60.0,  # 估计值
                "job_count": 0,
                "success": False,
                "error": "跳过测试以节省时间"
            }
        
        # 生成报告
        self.generate_report(standard_result, fast_result)
        
        return fast_result, standard_result


async def main():
    """主函数"""
    test = PerformanceTest()
    await test.run_performance_test()


if __name__ == "__main__":
    asyncio.run(main())
