"""
数据模型定义
定义职位信息的数据结构
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class JobPosition(BaseModel):
    """职位信息模型"""
    job_title: str = Field(..., description="岗位名称/职位标题")
    job_requirements: Optional[str] = Field("", description="岗位要求，包括技能要求、工作经验、学历等")
    salary_range: str = Field(..., description="薪资范围")
    company_name: str = Field(..., description="公司名称")
    company_scale: Optional[str] = Field(None, description="公司规模")
    company_industry: Optional[str] = Field(None, description="公司行业")
    job_detail_url: Optional[str] = Field("", description="职位详情页链接")
    location: Optional[str] = Field(None, description="工作地点")
    work_experience: Optional[str] = Field(None, description="工作经验要求")
    education: Optional[str] = Field(None, description="学历要求")
    job_description: Optional[str] = Field(None, description="职位描述")
    welfare_benefits: Optional[str] = Field(None, description="福利待遇")
    hr_info: Optional[str] = Field(None, description="HR信息")
    publish_time: Optional[str] = Field(None, description="发布时间")
    crawl_time: datetime = Field(default_factory=datetime.now, description="爬取时间")


class JobSearchResult(BaseModel):
    """搜索结果模型"""
    total_count: int = Field(0, description="总职位数量")
    current_page: int = Field(1, description="当前页码")
    jobs: List[JobPosition] = Field(default_factory=list, description="职位列表")
    search_keyword: Optional[str] = Field(None, description="搜索关键词")
    search_location: Optional[str] = Field(None, description="搜索地点")


class CrawlerConfig(BaseModel):
    """爬虫配置模型"""
    base_url: str = "https://www.zhipin.com"
    search_keyword: str
    search_location: str
    max_pages: int
    delay_range: tuple = (5, 12)
    max_retries: int = 10
    timeout: int = 300
    headless: bool = True
    use_proxy: bool = False
    proxy_url: Optional[str] = None
    output_format: str = "json"
    output_file: str = "boss_jobs.json"
    export_all_formats: bool = False
