#!/usr/bin/env python3
"""
性能基准测试 - 对比不同爬虫模式的性能
"""

import asyncio
import time
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List

from data_models import CrawlerConfig
from utils import Logger


class BenchmarkRunner:
    """性能基准测试运行器"""
    
    def __init__(self):
        self.results = []
    
    async def run_fast_mode_test(self, keyword: str, location: str, pages: int) -> Dict[str, Any]:
        """运行高性能模式测试"""
        Logger.info("⚡ 测试高性能模式...")
        
        try:
            from fast_crawler import FastBossCrawler
            
            config = CrawlerConfig(
                search_keyword=keyword,
                search_location=location,
                max_pages=pages,
                output_format="json",
                output_file=f"benchmark_fast_{keyword}_{location}_{pages}.json",
                headless=True,
                timeout=30,
                delay_range=(0.5, 1.0),
                max_retries=2
            )
            config.export_all_formats = False
            
            start_time = time.time()
            
            async with FastBossCrawler(config) as crawler:
                result = await crawler.fast_crawl(keyword, location, pages)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            return {
                "mode": "高性能模式",
                "execution_time": execution_time,
                "job_count": len(result.jobs),
                "success": True,
                "jobs_per_second": len(result.jobs) / execution_time if execution_time > 0 else 0,
                "pages_per_second": pages / execution_time if execution_time > 0 else 0
            }
            
        except Exception as e:
            Logger.error(f"❌ 高性能模式测试失败: {e}")
            return {
                "mode": "高性能模式",
                "execution_time": 0,
                "job_count": 0,
                "success": False,
                "error": str(e)
            }
    
    async def run_standard_mode_test(self, keyword: str, location: str, pages: int) -> Dict[str, Any]:
        """运行标准模式测试"""
        Logger.info("🎯 测试标准模式...")
        
        try:
            from ultimate_crawler import UltimateBossCrawler
            
            config = CrawlerConfig(
                search_keyword=keyword,
                search_location=location,
                max_pages=pages,
                output_format="json",
                output_file=f"benchmark_standard_{keyword}_{location}_{pages}.json",
                headless=True,
                timeout=300,
                delay_range=(5, 12),
                max_retries=10
            )
            
            start_time = time.time()
            
            async with UltimateBossCrawler(config) as crawler:
                result = await crawler.ultimate_crawl()
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            return {
                "mode": "标准模式",
                "execution_time": execution_time,
                "job_count": len(result.jobs),
                "success": True,
                "jobs_per_second": len(result.jobs) / execution_time if execution_time > 0 else 0,
                "pages_per_second": pages / execution_time if execution_time > 0 else 0
            }
            
        except Exception as e:
            Logger.error(f"❌ 标准模式测试失败: {e}")
            return {
                "mode": "标准模式",
                "execution_time": 0,
                "job_count": 0,
                "success": False,
                "error": str(e)
            }
    
    def generate_benchmark_report(self, fast_result: Dict[str, Any], standard_result: Dict[str, Any], 
                                 keyword: str, location: str, pages: int):
        """生成基准测试报告"""
        print("\n" + "=" * 80)
        print("🚀 BOSS直聘爬虫性能基准测试报告")
        print("=" * 80)
        
        print(f"\n📋 测试配置:")
        print(f"   关键词: {keyword}")
        print(f"   地点: {location}")
        print(f"   页数: {pages}")
        print(f"   测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n⏱️ 执行时间对比:")
        print(f"   高性能模式: {fast_result.get('execution_time', 0):.2f}秒")
        print(f"   标准模式: {standard_result.get('execution_time', 0):.2f}秒")
        
        if fast_result.get('success') and standard_result.get('success'):
            if fast_result['execution_time'] > 0:
                speedup = standard_result['execution_time'] / fast_result['execution_time']
                time_saved = standard_result['execution_time'] - fast_result['execution_time']
                print(f"   性能提升: {speedup:.1f}倍")
                print(f"   时间节省: {time_saved:.1f}秒")
        
        print(f"\n📊 数据量对比:")
        print(f"   高性能模式: {fast_result.get('job_count', 0)}个职位")
        print(f"   标准模式: {standard_result.get('job_count', 0)}个职位")
        
        print(f"\n🚀 吞吐量对比:")
        if fast_result.get('success'):
            print(f"   高性能模式: {fast_result.get('jobs_per_second', 0):.1f}个职位/秒, {fast_result.get('pages_per_second', 0):.1f}页/秒")
        if standard_result.get('success'):
            print(f"   标准模式: {standard_result.get('jobs_per_second', 0):.1f}个职位/秒, {standard_result.get('pages_per_second', 0):.1f}页/秒")
        
        # 性能目标检查
        target_time = 5.0
        print(f"\n🎯 性能目标检查:")
        if fast_result.get('success'):
            if fast_result['execution_time'] <= target_time:
                print(f"   ✅ 高性能模式达成目标 (≤{target_time}秒)")
            else:
                print(f"   ⚠️ 高性能模式未达成目标 (>{target_time}秒)")
        
        print(f"\n📈 成功率:")
        print(f"   高性能模式: {'✅ 成功' if fast_result.get('success') else '❌ 失败'}")
        print(f"   标准模式: {'✅ 成功' if standard_result.get('success') else '❌ 失败'}")
        
        if not fast_result.get('success'):
            print(f"   高性能模式错误: {fast_result.get('error', '未知错误')}")
        if not standard_result.get('success'):
            print(f"   标准模式错误: {standard_result.get('error', '未知错误')}")
        
        print(f"\n🏆 推荐使用:")
        if fast_result.get('success'):
            if fast_result['execution_time'] <= target_time:
                print("   🥇 高性能模式 - 速度快，满足性能目标")
            else:
                print("   🥈 高性能模式 - 速度较快，但未达到最佳目标")
        else:
            print("   🥉 标准模式 - 高性能模式不可用时的备选方案")
        
        print("=" * 80)
        
        # 保存结果
        self.results.append({
            "timestamp": time.time(),
            "config": {"keyword": keyword, "location": location, "pages": pages},
            "fast_mode": fast_result,
            "standard_mode": standard_result
        })
    
    def save_benchmark_results(self, filename: str = "benchmark_results.json"):
        """保存基准测试结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)
            Logger.info(f"📁 基准测试结果已保存到 {filename}")
        except Exception as e:
            Logger.error(f"❌ 保存基准测试结果失败: {e}")
    
    async def run_comprehensive_benchmark(self, test_cases: List[Dict[str, Any]]):
        """运行综合基准测试"""
        Logger.info("🚀 开始综合基准测试...")
        
        for i, case in enumerate(test_cases, 1):
            keyword = case['keyword']
            location = case['location']
            pages = case['pages']
            
            print(f"\n{'='*60}")
            print(f"📊 测试用例 {i}/{len(test_cases)}: {keyword} @ {location} ({pages}页)")
            print(f"{'='*60}")
            
            # 运行高性能模式测试
            fast_result = await self.run_fast_mode_test(keyword, location, pages)
            
            # 如果高性能模式很快，也测试标准模式进行对比
            if fast_result.get('success') and fast_result.get('execution_time', 0) < 10:
                standard_result = await self.run_standard_mode_test(keyword, location, pages)
            else:
                # 模拟标准模式结果
                standard_result = {
                    "mode": "标准模式",
                    "execution_time": 60.0,
                    "job_count": 0,
                    "success": False,
                    "error": "跳过测试以节省时间"
                }
            
            # 生成报告
            self.generate_benchmark_report(fast_result, standard_result, keyword, location, pages)
            
            # 短暂休息
            if i < len(test_cases):
                await asyncio.sleep(2)
        
        # 保存所有结果
        self.save_benchmark_results()


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BOSS直聘爬虫性能基准测试")
    parser.add_argument("--keyword", "-k", default="Python开发", help="搜索关键词")
    parser.add_argument("--location", "-l", default="北京", help="搜索地点")
    parser.add_argument("--pages", "-p", type=int, default=5, help="爬取页数")
    parser.add_argument("--comprehensive", "-c", action="store_true", help="运行综合测试")
    
    args = parser.parse_args()
    
    runner = BenchmarkRunner()
    
    if args.comprehensive:
        # 综合测试用例
        test_cases = [
            {"keyword": "Python开发", "location": "北京", "pages": 3},
            {"keyword": "Java工程师", "location": "上海", "pages": 3},
            {"keyword": "前端开发", "location": "深圳", "pages": 3},
            {"keyword": "数据分析师", "location": "杭州", "pages": 3},
        ]
        await runner.run_comprehensive_benchmark(test_cases)
    else:
        # 单个测试
        fast_result = await runner.run_fast_mode_test(args.keyword, args.location, args.pages)
        standard_result = await runner.run_standard_mode_test(args.keyword, args.location, args.pages)
        runner.generate_benchmark_report(fast_result, standard_result, args.keyword, args.location, args.pages)
        runner.save_benchmark_results()


if __name__ == "__main__":
    asyncio.run(main())
