#!/usr/bin/env python3
"""
性能监控工具 - 实时监控爬虫性能
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, Any, List
from datetime import datetime
from dataclasses import dataclass

from utils import Logger


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_tasks: int
    completed_tasks: int
    jobs_crawled: int


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, sample_interval: float = 1.0):
        self.sample_interval = sample_interval
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring = False
        self.monitor_thread = None
        self.start_time = None
        
        # 任务计数器
        self.active_tasks = 0
        self.completed_tasks = 0
        self.jobs_crawled = 0
        
        # 网络基线
        self.initial_network_stats = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.start_time = time.time()
        self.initial_network_stats = psutil.net_io_counters()
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        Logger.info("📊 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        
        Logger.info("📊 性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 收集性能指标
                cpu_percent = psutil.cpu_percent(interval=None)
                memory_info = psutil.virtual_memory()
                memory_mb = memory_info.used / 1024 / 1024
                
                # 网络统计
                current_network = psutil.net_io_counters()
                if self.initial_network_stats:
                    network_sent_mb = (current_network.bytes_sent - self.initial_network_stats.bytes_sent) / 1024 / 1024
                    network_recv_mb = (current_network.bytes_recv - self.initial_network_stats.bytes_recv) / 1024 / 1024
                else:
                    network_sent_mb = 0
                    network_recv_mb = 0
                
                # 创建性能指标
                metrics = PerformanceMetrics(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_mb=memory_mb,
                    network_sent_mb=network_sent_mb,
                    network_recv_mb=network_recv_mb,
                    active_tasks=self.active_tasks,
                    completed_tasks=self.completed_tasks,
                    jobs_crawled=self.jobs_crawled
                )
                
                self.metrics_history.append(metrics)
                
                # 保持历史记录在合理范围内
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-500:]
                
                time.sleep(self.sample_interval)
                
            except Exception as e:
                Logger.warning(f"⚠️ 性能监控异常: {e}")
                time.sleep(self.sample_interval)
    
    def increment_active_tasks(self):
        """增加活跃任务数"""
        self.active_tasks += 1
    
    def decrement_active_tasks(self):
        """减少活跃任务数"""
        self.active_tasks = max(0, self.active_tasks - 1)
        self.completed_tasks += 1
    
    def add_jobs_crawled(self, count: int):
        """添加爬取的职位数"""
        self.jobs_crawled += count
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        if not self.metrics_history:
            return {}
        
        latest = self.metrics_history[-1]
        runtime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "runtime_seconds": runtime,
            "cpu_percent": latest.cpu_percent,
            "memory_mb": latest.memory_mb,
            "network_sent_mb": latest.network_sent_mb,
            "network_recv_mb": latest.network_recv_mb,
            "active_tasks": latest.active_tasks,
            "completed_tasks": latest.completed_tasks,
            "jobs_crawled": latest.jobs_crawled,
            "jobs_per_second": latest.jobs_crawled / runtime if runtime > 0 else 0,
            "tasks_per_second": latest.completed_tasks / runtime if runtime > 0 else 0
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {}
        
        # 计算统计信息
        cpu_values = [m.cpu_percent for m in self.metrics_history]
        memory_values = [m.memory_mb for m in self.metrics_history]
        
        runtime = time.time() - self.start_time if self.start_time else 0
        latest = self.metrics_history[-1]
        
        return {
            "runtime_seconds": runtime,
            "total_samples": len(self.metrics_history),
            "cpu": {
                "current": latest.cpu_percent,
                "average": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory": {
                "current_mb": latest.memory_mb,
                "average_mb": sum(memory_values) / len(memory_values),
                "max_mb": max(memory_values),
                "min_mb": min(memory_values)
            },
            "network": {
                "sent_mb": latest.network_sent_mb,
                "recv_mb": latest.network_recv_mb,
                "total_mb": latest.network_sent_mb + latest.network_recv_mb
            },
            "tasks": {
                "active": latest.active_tasks,
                "completed": latest.completed_tasks,
                "total": latest.active_tasks + latest.completed_tasks
            },
            "jobs": {
                "total_crawled": latest.jobs_crawled,
                "jobs_per_second": latest.jobs_crawled / runtime if runtime > 0 else 0
            }
        }
    
    def print_performance_report(self):
        """打印性能报告"""
        summary = self.get_performance_summary()
        if not summary:
            Logger.warning("⚠️ 没有性能数据可显示")
            return
        
        print("\n" + "=" * 60)
        print("📊 性能监控报告")
        print("=" * 60)
        
        print(f"\n⏱️ 运行时间: {summary['runtime_seconds']:.1f}秒")
        print(f"📈 采样次数: {summary['total_samples']}")
        
        cpu = summary['cpu']
        print(f"\n🖥️ CPU使用率:")
        print(f"   当前: {cpu['current']:.1f}%")
        print(f"   平均: {cpu['average']:.1f}%")
        print(f"   最高: {cpu['max']:.1f}%")
        print(f"   最低: {cpu['min']:.1f}%")
        
        memory = summary['memory']
        print(f"\n💾 内存使用:")
        print(f"   当前: {memory['current_mb']:.1f}MB")
        print(f"   平均: {memory['average_mb']:.1f}MB")
        print(f"   最高: {memory['max_mb']:.1f}MB")
        print(f"   最低: {memory['min_mb']:.1f}MB")
        
        network = summary['network']
        print(f"\n🌐 网络流量:")
        print(f"   发送: {network['sent_mb']:.2f}MB")
        print(f"   接收: {network['recv_mb']:.2f}MB")
        print(f"   总计: {network['total_mb']:.2f}MB")
        
        tasks = summary['tasks']
        print(f"\n📋 任务统计:")
        print(f"   活跃: {tasks['active']}")
        print(f"   完成: {tasks['completed']}")
        print(f"   总计: {tasks['total']}")
        
        jobs = summary['jobs']
        print(f"\n🎯 爬取统计:")
        print(f"   职位总数: {jobs['total_crawled']}")
        print(f"   爬取速度: {jobs['jobs_per_second']:.1f}个/秒")
        
        print("=" * 60)
    
    def export_metrics(self, filename: str = "performance_metrics.json"):
        """导出性能指标"""
        try:
            import json
            
            data = {
                "summary": self.get_performance_summary(),
                "metrics_history": [
                    {
                        "timestamp": m.timestamp,
                        "cpu_percent": m.cpu_percent,
                        "memory_mb": m.memory_mb,
                        "network_sent_mb": m.network_sent_mb,
                        "network_recv_mb": m.network_recv_mb,
                        "active_tasks": m.active_tasks,
                        "completed_tasks": m.completed_tasks,
                        "jobs_crawled": m.jobs_crawled
                    }
                    for m in self.metrics_history
                ]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            Logger.info(f"📁 性能指标已导出到 {filename}")
            
        except Exception as e:
            Logger.error(f"❌ 导出性能指标失败: {e}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def start_performance_monitoring():
    """启动性能监控"""
    performance_monitor.start_monitoring()


def stop_performance_monitoring():
    """停止性能监控"""
    performance_monitor.stop_monitoring()


def get_performance_summary():
    """获取性能摘要"""
    return performance_monitor.get_performance_summary()


def print_performance_report():
    """打印性能报告"""
    performance_monitor.print_performance_report()


# 装饰器：监控异步函数性能
def monitor_async_task(func):
    """监控异步任务的装饰器"""
    async def wrapper(*args, **kwargs):
        performance_monitor.increment_active_tasks()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            performance_monitor.decrement_active_tasks()
    return wrapper


if __name__ == "__main__":
    # 测试性能监控
    async def test_monitoring():
        start_performance_monitoring()
        
        # 模拟一些工作
        for i in range(5):
            performance_monitor.increment_active_tasks()
            await asyncio.sleep(1)
            performance_monitor.add_jobs_crawled(10)
            performance_monitor.decrement_active_tasks()
        
        print_performance_report()
        stop_performance_monitoring()
    
    asyncio.run(test_monitoring())
