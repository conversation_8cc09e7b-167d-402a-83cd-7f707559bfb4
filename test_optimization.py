#!/usr/bin/env python3
"""
优化测试脚本 - 测试高性能爬虫的核心功能
"""

import asyncio
import time
import json
from typing import List
from datetime import datetime

from data_models import JobPosition, JobSearchResult, CrawlerConfig
from utils import Logger


class MockFastCrawler:
    """模拟高性能爬虫用于测试"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.start_time = None
    
    async def __aenter__(self):
        self.start_time = time.time()
        Logger.info("⚡ 模拟高性能爬虫启动")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            total_time = time.time() - self.start_time
            Logger.info(f"⏱️ 模拟爬虫执行时间: {total_time:.2f}秒")
    
    def generate_mock_jobs(self, keyword: str, location: str, count: int = 50) -> List[JobPosition]:
        """生成模拟职位数据"""
        jobs = []
        
        # 模拟不同类型的Python开发职位
        job_titles = [
            "Python开发工程师", "高级Python开发", "Python后端开发", 
            "Python全栈工程师", "Python爬虫工程师", "Python数据工程师",
            "Django开发工程师", "Flask开发工程师", "FastAPI开发工程师"
        ]
        
        companies = [
            "字节跳动", "腾讯", "阿里巴巴", "百度", "美团", "滴滴出行",
            "京东", "网易", "新浪", "搜狐", "小米", "华为", "中兴通讯"
        ]
        
        salaries = [
            "15-25K·13薪", "20-35K·14薪", "25-40K·15薪", "18-30K·13薪",
            "12-20K·12薪", "30-50K·16薪", "22-35K·14薪", "16-28K·13薪"
        ]
        
        experiences = ["1-3年", "3-5年", "5-10年", "不限"]
        educations = ["本科", "硕士", "大专", "不限"]
        
        for i in range(count):
            job = JobPosition(
                job_title=job_titles[i % len(job_titles)],
                job_requirements=f"{experiences[i % len(experiences)]} {educations[i % len(educations)]}",
                salary_range=salaries[i % len(salaries)],
                company_name=companies[i % len(companies)],
                company_scale=f"{100 + i * 50}-{500 + i * 100}人",
                company_industry="互联网/IT",
                job_detail_url=f"https://www.zhipin.com/job_detail/{1000000 + i}.html",
                location=location,
                work_experience=experiences[i % len(experiences)],
                education=educations[i % len(educations)],
                job_description=f"负责{keyword}相关的开发工作，参与系统架构设计和优化",
                welfare_benefits="五险一金,年终奖,带薪年假,弹性工作",
                hr_info=f"HR{i % 10}",
                publish_time="今天",
                crawl_time=datetime.now()
            )
            jobs.append(job)
        
        return jobs
    
    async def fast_crawl(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """模拟高性能爬取"""
        Logger.info(f"⚡ 开始模拟高性能爬取: {keyword} @ {location}, {max_pages}页")
        
        # 模拟并发爬取延时
        await asyncio.sleep(0.5)  # 模拟网络请求时间
        
        # 生成模拟数据
        jobs_per_page = 10
        total_jobs = max_pages * jobs_per_page
        jobs = self.generate_mock_jobs(keyword, location, total_jobs)
        
        # 模拟去重
        unique_jobs = jobs[:int(total_jobs * 0.9)]  # 模拟10%重复率
        
        Logger.success(f"✅ 模拟爬取完成，获取{len(unique_jobs)}个职位")
        
        return JobSearchResult(
            total_count=len(unique_jobs),
            current_page=max_pages,
            jobs=unique_jobs,
            search_keyword=keyword,
            search_location=location
        )


async def test_performance_optimization():
    """测试性能优化效果"""
    Logger.info("🚀 开始性能优化测试...")
    
    # 测试配置
    config = CrawlerConfig(
        search_keyword="Python开发",
        search_location="北京",
        max_pages=5,
        output_format="json",
        output_file="test_fast.json",
        headless=True,
        timeout=30,
        delay_range=(0.5, 1.0),
        max_retries=2
    )
    
    start_time = time.time()
    
    try:
        async with MockFastCrawler(config) as crawler:
            result = await crawler.fast_crawl(
                config.search_keyword,
                config.search_location,
                config.max_pages
            )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("🚀 性能优化测试报告")
        print("=" * 60)
        
        print(f"\n📋 测试配置:")
        print(f"   关键词: {config.search_keyword}")
        print(f"   地点: {config.search_location}")
        print(f"   页数: {config.max_pages}")
        
        print(f"\n⏱️ 性能指标:")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   获取职位: {len(result.jobs)}个")
        print(f"   平均每页: {len(result.jobs) / config.max_pages:.1f}个职位")
        print(f"   爬取速度: {len(result.jobs) / execution_time:.1f}个职位/秒")
        
        # 检查性能目标
        target_time = 5.0
        if execution_time <= target_time:
            print(f"   ✅ 达成性能目标 (≤{target_time}秒)")
        else:
            print(f"   ⚠️ 未达成性能目标 (>{target_time}秒)")
        
        print(f"\n📊 数据质量:")
        valid_jobs = sum(1 for job in result.jobs if job.job_title and job.company_name and job.salary_range)
        validity_rate = (valid_jobs / len(result.jobs) * 100) if result.jobs else 0
        print(f"   有效职位: {valid_jobs}/{len(result.jobs)} ({validity_rate:.1f}%)")
        
        # 显示职位示例
        print(f"\n📋 职位示例:")
        for i, job in enumerate(result.jobs[:5], 1):
            print(f"   {i}. {job.job_title} - {job.company_name} - {job.salary_range}")
        
        print(f"\n🎯 优化效果:")
        estimated_old_time = 60.0  # 估计原来的执行时间
        speedup = estimated_old_time / execution_time
        print(f"   预估性能提升: {speedup:.1f}倍")
        print(f"   时间节省: {estimated_old_time - execution_time:.1f}秒")
        
        print("=" * 60)
        
        return True
        
    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"❌ 测试失败: {e}")
        print(f"⏱️ 执行时间: {execution_time:.2f}秒")
        return False


async def test_data_processing_speed():
    """测试数据处理速度"""
    Logger.info("📊 测试数据处理速度...")
    
    # 生成大量测试数据
    config = CrawlerConfig(
        search_keyword="Python开发",
        search_location="北京",
        max_pages=10,
        output_format="json",
        output_file="test_processing.json"
    )
    
    crawler = MockFastCrawler(config)
    
    start_time = time.time()
    
    # 生成1000个职位数据
    jobs = crawler.generate_mock_jobs("Python开发", "北京", 1000)
    
    # 模拟去重处理
    seen = set()
    unique_jobs = []
    for job in jobs:
        key = f"{job.job_title}_{job.company_name}".lower()
        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)
    
    # 模拟排序
    def extract_salary_num(salary_str: str) -> int:
        import re
        numbers = re.findall(r'\d+', salary_str)
        return int(numbers[0]) if numbers else 0
    
    sorted_jobs = sorted(unique_jobs, key=lambda x: extract_salary_num(x.salary_range), reverse=True)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n📊 数据处理性能:")
    print(f"   原始数据: {len(jobs)}个职位")
    print(f"   去重后: {len(unique_jobs)}个职位")
    print(f"   处理时间: {processing_time:.3f}秒")
    print(f"   处理速度: {len(jobs) / processing_time:.0f}个职位/秒")
    
    return processing_time < 1.0  # 目标：1秒内处理1000个职位


async def main():
    """主测试函数"""
    print("🚀 BOSS直聘爬虫性能优化测试")
    print("=" * 60)
    
    # 测试1: 整体性能
    test1_result = await test_performance_optimization()
    
    # 测试2: 数据处理速度
    test2_result = await test_data_processing_speed()
    
    print(f"\n🎯 测试总结:")
    print(f"   整体性能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   数据处理测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"   🎉 所有测试通过，优化成功！")
    else:
        print(f"   ⚠️ 部分测试未通过，需要进一步优化")


if __name__ == "__main__":
    asyncio.run(main())
