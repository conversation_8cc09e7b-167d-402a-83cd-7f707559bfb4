#!/usr/bin/env python3
"""
最终集成测试 - 验证所有优化功能
"""

import asyncio
import time
import subprocess
import sys
from pathlib import Path

def print_header(title: str):
    """打印测试标题"""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def print_result(test_name: str, success: bool, time_taken: float = None):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    time_info = f" ({time_taken:.2f}秒)" if time_taken else ""
    print(f"   {test_name}: {status}{time_info}")

async def test_imports():
    """测试模块导入"""
    print_header("模块导入测试")
    
    tests = [
        ("data_models", "from data_models import CrawlerConfig, JobPosition"),
        ("utils", "from utils import Logger, DataExporter"),
        ("fast_crawler", "from fast_crawler import FastBossCrawler"),
        ("performance_monitor", "from performance_monitor import performance_monitor"),
        ("ultimate_crawler", "from ultimate_crawler import UltimateBossCrawler"),
    ]
    
    all_passed = True
    for name, import_code in tests:
        try:
            exec(import_code)
            print_result(f"{name} 模块", True)
        except Exception as e:
            print_result(f"{name} 模块", False)
            print(f"      错误: {e}")
            all_passed = False
    
    return all_passed

async def test_fast_crawler_basic():
    """测试高性能爬虫基础功能"""
    print_header("高性能爬虫基础功能测试")
    
    try:
        from fast_crawler import FastBossCrawler
        from data_models import CrawlerConfig
        
        # 创建配置
        config = CrawlerConfig(
            search_keyword="Python开发",
            search_location="北京",
            max_pages=2,
            output_format="json",
            output_file="test_fast.json"
        )
        
        # 测试爬虫创建
        start_time = time.time()
        crawler = FastBossCrawler(config)
        creation_time = time.time() - start_time
        print_result("爬虫对象创建", True, creation_time)
        
        # 测试URL生成
        start_time = time.time()
        urls = crawler.generate_search_urls("Python开发", "北京", 3)
        url_time = time.time() - start_time
        print_result(f"URL生成 ({len(urls)}个)", len(urls) == 3, url_time)
        
        # 测试去重功能
        from data_models import JobPosition
        from datetime import datetime
        
        jobs = [
            JobPosition(job_title="Python开发", company_name="公司A", salary_range="15-25K", crawl_time=datetime.now()),
            JobPosition(job_title="Python开发", company_name="公司A", salary_range="15-25K", crawl_time=datetime.now()),
            JobPosition(job_title="Java开发", company_name="公司B", salary_range="20-30K", crawl_time=datetime.now()),
        ]
        
        start_time = time.time()
        unique_jobs = crawler._fast_deduplication(jobs)
        dedup_time = time.time() - start_time
        print_result(f"去重功能 ({len(unique_jobs)}/3)", len(unique_jobs) == 2, dedup_time)
        
        return True
        
    except Exception as e:
        print_result("高性能爬虫测试", False)
        print(f"      错误: {e}")
        return False

async def test_performance_monitor():
    """测试性能监控"""
    print_header("性能监控测试")
    
    try:
        from performance_monitor import PerformanceMonitor
        
        # 创建监控器
        start_time = time.time()
        monitor = PerformanceMonitor(sample_interval=0.1)
        creation_time = time.time() - start_time
        print_result("监控器创建", True, creation_time)
        
        # 启动监控
        start_time = time.time()
        monitor.start_monitoring()
        await asyncio.sleep(0.5)  # 监控0.5秒
        monitor.stop_monitoring()
        monitor_time = time.time() - start_time
        print_result("监控运行", True, monitor_time)
        
        # 测试指标获取
        start_time = time.time()
        metrics = monitor.get_performance_summary()
        metrics_time = time.time() - start_time
        print_result("指标获取", bool(metrics), metrics_time)
        
        return True
        
    except Exception as e:
        print_result("性能监控测试", False)
        print(f"      错误: {e}")
        return False

def test_command_line():
    """测试命令行参数"""
    print_header("命令行参数测试")
    
    try:
        # 测试帮助信息
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        help_success = result.returncode == 0 and "--fast" in result.stdout
        print_result("帮助信息", help_success)
        
        # 测试参数解析（不实际运行）
        test_args = [
            ["--keyword", "Python开发", "--location", "北京", "--pages", "3"],
            ["--keyword", "Java开发", "--location", "上海", "--pages", "5", "--fast"],
            ["--keyword", "前端开发", "--location", "深圳", "--pages", "2", "--format", "json"],
        ]
        
        args_success = True
        for args in test_args:
            try:
                # 这里只测试参数解析，不实际运行爬虫
                cmd = [sys.executable, "-c", f"""
import sys
sys.path.insert(0, '.')
from main import create_argument_parser
parser = create_argument_parser()
args = parser.parse_args({args})
print(f'参数解析成功: {{args.keyword}}, {{args.location}}, {{args.pages}}')
"""]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
                if result.returncode != 0:
                    args_success = False
                    break
            except:
                args_success = False
                break
        
        print_result("参数解析", args_success)
        return help_success and args_success
        
    except Exception as e:
        print_result("命令行测试", False)
        print(f"      错误: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print_header("文件结构测试")
    
    required_files = [
        "main.py",
        "fast_crawler.py",
        "performance_monitor.py",
        "data_models.py",
        "utils.py",
        "requirements.txt",
        "README.md",
        "PERFORMANCE_GUIDE.md"
    ]
    
    all_exist = True
    for file_name in required_files:
        exists = Path(file_name).exists()
        print_result(f"{file_name}", exists)
        if not exists:
            all_exist = False
    
    return all_exist

async def run_integration_test():
    """运行完整的集成测试"""
    print("🚀 BOSS直聘爬虫优化 - 最终集成测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 文件结构测试
    result1 = test_file_structure()
    test_results.append(("文件结构", result1))
    
    # 2. 模块导入测试
    result2 = await test_imports()
    test_results.append(("模块导入", result2))
    
    # 3. 高性能爬虫测试
    result3 = await test_fast_crawler_basic()
    test_results.append(("高性能爬虫", result3))
    
    # 4. 性能监控测试
    result4 = await test_performance_monitor()
    test_results.append(("性能监控", result4))
    
    # 5. 命令行测试
    result5 = test_command_line()
    test_results.append(("命令行参数", result5))
    
    # 生成最终报告
    print_header("最终测试报告")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        print_result(test_name, result)
    
    print(f"\n📊 测试统计:")
    print(f"   通过: {passed_tests}/{total_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！优化成功完成！")
        print(f"\n🚀 可以使用以下命令体验高性能模式:")
        print(f"   python main.py --keyword \"Python开发\" --location \"北京\" --pages 5 --fast")
    else:
        print(f"\n⚠️ 部分测试未通过，请检查相关功能")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    try:
        result = asyncio.run(run_integration_test())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
