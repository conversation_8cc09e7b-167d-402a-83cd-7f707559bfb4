#!/usr/bin/env python3
"""
快速测试脚本 - 验证高性能爬虫
"""

import asyncio
import time
from fast_crawler import fast_crawl_boss_jobs
from utils import Logger


async def quick_test():
    """快速测试高性能爬虫"""
    Logger.info("🚀 开始快速测试...")
    
    start_time = time.time()
    
    try:
        result = await fast_crawl_boss_jobs(
            keyword="Python开发",
            location="北京",
            max_pages=5,
            output_format="all"
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"\n✅ 测试完成!")
        print(f"⏱️ 执行时间: {execution_time:.2f}秒")
        print(f"📊 获取职位: {len(result.jobs)}个")
        
        if execution_time <= 5.0:
            print("🎯 达成性能目标 (≤5秒)")
        else:
            print("⚠️ 未达成性能目标 (>5秒)")
        
        # 显示部分职位信息
        if result.jobs:
            print(f"\n📋 职位示例:")
            for i, job in enumerate(result.jobs[:3], 1):
                print(f"   {i}. {job.job_title} - {job.company_name} - {job.salary_range}")
        
    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"❌ 测试失败: {e}")
        print(f"⏱️ 执行时间: {execution_time:.2f}秒")


if __name__ == "__main__":
    asyncio.run(quick_test())
