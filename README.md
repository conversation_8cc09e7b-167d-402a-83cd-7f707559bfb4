# BOSS直聘职位爬虫

基于 crawl4ai 的高性能职位信息爬虫，支持多策略并行爬取和智能反检测。

## 安装

```bash
pip install -r requirements.txt
playwright install
```

## 使用

```bash
# 基本使用（必须指定关键词、地点、页数）
python main.py --keyword "Python开发" --location "北京" --pages 5

# 高性能模式（5秒内完成5页爬取）
python main.py --keyword "Python开发" --location "北京" --pages 5 --fast

# 指定输出格式
python main.py --keyword "Java工程师" --location "上海" --pages 10 --format json

# 默认同时输出json和excel格式
python main.py --keyword "数据分析师" --location "杭州" --pages 5
```

## 主要参数

- `--keyword` / `-k`: 搜索关键词（必填）
- `--location` / `-l`: 搜索地点（必填）
- `--pages` / `-p`: 爬取页数（必填）
- `--format` / `-f`: 输出格式 json/csv/excel/all（默认: all）
- `--fast`: 高性能模式（5秒内完成5页爬取）
- `--headless`: 无头模式 true/false（默认: true）
- `--proxy`: 代理服务器地址
- `--verbose` / `-v`: 详细日志

## 输出数据

包含岗位名称、薪资范围、公司信息、工作地点、经验要求、学历要求、职位描述、HR信息、发布时间等完整字段。

## 注意事项

仅供学习研究使用，请遵守网站使用条款。
