#!/usr/bin/env python3
"""
BOSS直聘职位爬虫 - 主程序入口
使用crawl4ai框架实现的高性能职位信息爬虫

使用方法:
    python main.py                          # 使用默认参数
    python main.py --keyword "Java开发"      # 指定搜索关键词
    python main.py --location "上海"         # 指定搜索地点
    python main.py --pages 5                # 指定爬取页数
    python main.py --format csv             # 指定输出格式
"""

import asyncio
import argparse
import sys
from pathlib import Path

from data_models import CrawlerConfig
from ultimate_crawler import UltimateBossCrawler
from fast_crawler import FastBossCrawler
from utils import Logger, format_job_summary


def create_argument_parser():
    """创建参数解析器"""
    parser = argparse.ArgumentParser(
        description="BOSS直聘职位信息爬虫",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                                    # 默认爬取北京Python职位
  python main.py --keyword "Java开发" --location "上海"  # 爬取上海Java开发职位
  python main.py --pages 10 --format excel          # 爬取10页并导出Excel
  python main.py --headless false                   # 显示浏览器界面
        """
    )

    parser.add_argument(
        "--keyword", "-k",
        type=str,
        required=True,
        help="搜索关键词 (必填)"
    )

    parser.add_argument(
        "--location", "-l",
        type=str,
        required=True,
        help="搜索地点 (必填)"
    )

    parser.add_argument(
        "--pages", "-p",
        type=int,
        required=True,
        help="爬取页数 (必填)"
    )

    parser.add_argument(
        "--format", "-f",
        type=str,
        choices=["json", "csv", "excel", "all"],
        default="all",
        help="输出格式 (默认: all - 同时输出json和excel)"
    )

    parser.add_argument(
        "--output", "-o",
        type=str,
        help="输出文件名 (默认: 根据格式自动生成)"
    )

    parser.add_argument(
        "--headless",
        type=str,
        choices=["true", "false"],
        default="true",
        help="是否无头模式运行 (默认: true)"
    )

    parser.add_argument(
        "--delay",
        type=float,
        default=3.0,
        help="请求间隔时间(秒) (默认: 3.0)"
    )

    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="页面超时时间(秒) (默认: 30)"
    )

    parser.add_argument(
        "--proxy",
        type=str,
        help="代理服务器地址 (格式: http://host:port)"
    )

    parser.add_argument(
        "--detail",
        action="store_true",
        help="是否爬取职位详情页 (会显著增加爬取时间)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细日志"
    )

    parser.add_argument(
        "--fast",
        action="store_true",
        help="使用高性能模式 (5秒内完成5页爬取)"
    )

    return parser

def parse_arguments():
    """解析命令行参数"""
    parser = create_argument_parser()
    return parser.parse_args()


def generate_output_filename(keyword: str, location: str, format_type: str) -> str:
    """生成输出文件名"""
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    safe_keyword = "".join(c for c in keyword if c.isalnum() or c in "._-")
    safe_location = "".join(c for c in location if c.isalnum() or c in "._-")
    
    extensions = {
        "json": ".json",
        "csv": ".csv", 
        "excel": ".xlsx"
    }
    
    ext = extensions.get(format_type, ".json")
    return f"boss_{safe_keyword}_{safe_location}_{timestamp}{ext}"


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 生成输出文件名
        if not args.output:
            output_file = generate_output_filename(args.keyword, args.location, args.format)
        else:
            output_file = args.output
        
        # 处理输出格式
        if args.format == "all":
            output_format = "json"  # 主要格式
            export_all_formats = True
        else:
            output_format = args.format
            export_all_formats = False

        # 创建爬虫配置
        config = CrawlerConfig(
            search_keyword=args.keyword,
            search_location=args.location,
            max_pages=args.pages,
            headless=(args.headless.lower() == "true"),
            timeout=300,  # 增加超时时间
            use_proxy=bool(args.proxy),
            proxy_url=args.proxy,
            output_format=output_format,
            output_file=output_file,
            delay_range=(5, 12),  # 优化延时范围
            max_retries=10  # 增加重试次数
        )

        # 设置导出标志
        config.export_all_formats = export_all_formats
        
        # 显示爬取配置
        Logger.info("🚀 BOSS直聘职位爬虫启动")
        Logger.info(f"📋 爬取配置:")
        Logger.info(f"   关键词: {config.search_keyword}")
        Logger.info(f"   地点: {config.search_location}")
        Logger.info(f"   页数: {config.max_pages}")
        Logger.info(f"   输出格式: {config.output_format}")
        Logger.info(f"   输出文件: {config.output_file}")
        Logger.info(f"   无头模式: {config.headless}")
        if config.use_proxy:
            Logger.info(f"   代理: {config.proxy_url}")
        
        # 创建输出目录
        output_path = Path(config.output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 选择爬虫类型
        if args.fast:
            Logger.info("⚡ 使用高性能模式")
            async with FastBossCrawler(config) as crawler:
                result = await crawler.fast_crawl(
                    config.search_keyword,
                    config.search_location,
                    config.max_pages
                )
        else:
            Logger.info("🎯 使用标准模式")
            async with UltimateBossCrawler(config) as crawler:
                result = await crawler.ultimate_crawl()
            
            # 显示结果摘要
            if result.jobs:
                Logger.success("🎉 爬取完成!")
                print("\n" + format_job_summary(result.jobs))
                
                # 显示文件信息
                if output_path.exists():
                    file_size = output_path.stat().st_size
                    Logger.info(f"📁 输出文件: {config.output_file} ({file_size} bytes)")
            else:
                Logger.warning("⚠️ 未获取到任何职位信息")
                
    except KeyboardInterrupt:
        Logger.warning("⏹️ 用户中断爬取")
        sys.exit(1)
    except Exception as e:
        Logger.error(f"💥 程序运行出错: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
